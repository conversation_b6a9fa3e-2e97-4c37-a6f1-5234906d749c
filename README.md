# Add Function DLL Project

这个项目演示了如何将一个简单的add函数封装成Windows DLL，并提供了两种测试方式。

## 文件结构

- `add.h` - 头文件，包含DLL导出声明
- `add.c` - 实现文件，包含add函数的实现
- `add.dll` - 编译生成的动态链接库
- `add.lib` - 编译生成的导入库
- `test_dll.c` - 动态加载DLL的测试程序
- `test_static.c` - 静态链接的测试程序
- `build.bat` - 编译脚本
- `Makefile` - Make编译文件

## 编译方法

### 方法1：使用批处理脚本
```cmd
.\build.bat
```

### 方法2：使用Make
```cmd
make
```

### 方法3：手动编译
```cmd
# 编译DLL
gcc -shared -o add.dll add.c "-Wl,--out-implib,add.lib"

# 编译动态链接测试程序
gcc -o test_dll.exe test_dll.c

# 编译静态链接测试程序
gcc -o test_static.exe test_static.c add.lib
```

## 运行测试

### 动态链接测试
```cmd
.\test_dll.exe
```
这个程序在运行时动态加载add.dll，需要确保add.dll在同一目录下。

### 静态链接测试
```cmd
.\test_static.exe
```
这个程序在编译时就链接了add函数，可以独立运行。

## DLL导出说明

在`add.h`中使用了以下宏来处理DLL导出：
- `BUILDING_DLL` - 在编译DLL时定义，使函数被导出
- `DLL_EXPORT` - 根据编译环境自动选择`__declspec(dllexport)`或`__declspec(dllimport)`

## 测试结果

两个测试程序都会输出以下结果：
```
add(3, 2) = 5
add(10, 20) = 30
add(-5, 8) = 3
add(0, 0) = 0
```

## 清理

要清理生成的文件，可以使用：
```cmd
make clean
```
或手动删除：
```cmd
del *.dll *.lib *.exe
```
