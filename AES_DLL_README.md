# AES128-CMAC DLL 使用说明

这个项目将AES128-CMAC加密算法封装成Windows DLL，方便其他程序调用而无需移植代码。

## 🎯 主要功能

- **AES128-CMAC加密算法**：完整的RFC 4493标准实现
- **DLL封装**：支持动态链接和静态链接两种方式
- **简单易用**：提供清晰的API接口
- **无外部依赖**：完全独立的实现

## 📁 文件结构

### 核心文件
- `aes128_cmac.h` - 头文件，包含API声明
- `aes128_cmac.c` - 实现文件，包含完整的AES128-CMAC算法
- `aes128_cmac.dll` - 编译生成的动态链接库
- `aes128_cmac.lib` - 编译生成的导入库

### 测试程序
- `test_aes_dll.c` - 动态加载DLL的完整测试程序
- `test_aes_static.c` - 静态链接的测试程序
- `simple_aes_example.c` - 简单使用示例

### 编译脚本
- `build_aes.bat` - 编译脚本

## 🔧 编译方法

### 使用批处理脚本（推荐）
```cmd
.\build_aes.bat
```

### 手动编译
```cmd
# 编译DLL
gcc -shared -o aes128_cmac.dll aes128_cmac.c "-Wl,--out-implib,aes128_cmac.lib"

# 编译动态链接测试程序
gcc -o test_aes_dll.exe test_aes_dll.c

# 编译静态链接测试程序
gcc -o test_aes_static.exe test_aes_static.c aes128_cmac.lib

# 编译简单示例
gcc -o simple_aes_example.exe simple_aes_example.c
```

## 🚀 使用方法

### 方法1：动态加载DLL（推荐给第三方使用）

```c
#include <windows.h>
#include <stdint.h>

// 定义函数指针
typedef void (*AES128_CMAC_Calculate_Func)(const uint8_t *key, const uint8_t *message, size_t message_len, uint8_t *mac);

int main() {
    // 加载DLL
    HMODULE hDll = LoadLibrary("aes128_cmac.dll");
    
    // 获取函数地址
    AES128_CMAC_Calculate_Func AES128_CMAC_Calculate = 
        (AES128_CMAC_Calculate_Func)GetProcAddress(hDll, "AES128_CMAC_Calculate");
    
    // 使用函数
    uint8_t key[16] = {0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
                       0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A};
    uint8_t message[16] = {0x1B, 0x36, 0xB4, 0xD3, 0x72, 0xB0, 0x33, 0xEA,
                           0x7E, 0x2F, 0xD7, 0x96, 0x25, 0xCC, 0xD0, 0x0C};
    uint8_t mac[16];
    
    AES128_CMAC_Calculate(key, message, 16, mac);
    
    // 释放DLL
    FreeLibrary(hDll);
    return 0;
}
```

### 方法2：静态链接

```c
#include "aes128_cmac.h"

int main() {
    uint8_t key[16] = {0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
                       0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A};
    uint8_t message[16] = {0x1B, 0x36, 0xB4, 0xD3, 0x72, 0xB0, 0x33, 0xEA,
                           0x7E, 0x2F, 0xD7, 0x96, 0x25, 0xCC, 0xD0, 0x0C};
    uint8_t mac[16];
    
    AES128_CMAC_Calculate(key, message, 16, mac);
    return 0;
}
```

## 📊 测试结果

使用提供的密钥和种子：
- **密钥**: `7D A2 58 19 E3 35 8F 52 B4 26 99 C7 74 11 E8 5A`
- **输入种子**: `1B 36 B4 D3 72 B0 33 EA 7E 2F D7 96 25 CC D0 0C`
- **CMAC结果**: `99 64 E7 5D 85 DF 04 12 8C C9 BB 8E 15 7D 76 19`

## 🔍 API 参考

### AES128_CMAC_Calculate
```c
void AES128_CMAC_Calculate(const uint8_t *key, const uint8_t *message, size_t message_len, uint8_t *mac);
```
- `key`: 16字节的AES128密钥
- `message`: 输入消息数据
- `message_len`: 消息长度（字节）
- `mac`: 输出缓冲区，16字节的MAC结果

### 其他可用函数
- `AES128_CMAC_Generate_Subkeys()` - 生成CMAC子密钥
- `AES128_CMAC_XOR_Block()` - 16字节块异或运算
- `AES128_CMAC_Left_Shift()` - 16字节块左移1位
- `AES128_CMAC_Test()` - 内置测试函数

## 🎯 给第三方的使用建议

1. **只需要两个文件**：`aes128_cmac.dll` 和你的程序
2. **简单调用**：参考 `simple_aes_example.c` 的实现
3. **无需移植**：直接调用DLL函数即可
4. **跨平台兼容**：DLL在Windows系统上通用

## ⚠️ 注意事项

- 确保 `aes128_cmac.dll` 与你的程序在同一目录
- 密钥和消息数据必须是有效的内存地址
- MAC输出缓冲区必须至少16字节
- 函数是线程安全的
