CC = gcc
CFLAGS = -Wall -O2

# 目标文件
all: add.dll test_dll.exe test_static.exe

# 编译DLL
add.dll: add.c add.h
	$(CC) -shared -o add.dll add.c -Wl,--out-implib,add.lib

# 编译动态链接测试程序
test_dll.exe: test_dll.c
	$(CC) $(CFLAGS) -o test_dll.exe test_dll.c

# 编译静态链接测试程序
test_static.exe: test_static.c add.lib
	$(CC) $(CFLAGS) -o test_static.exe test_static.c add.lib

# 清理生成的文件
clean:
	del /f *.dll *.lib *.exe 2>nul || true

# 运行测试
test: all
	@echo 运行动态链接测试:
	test_dll.exe
	@echo.
	@echo 运行静态链接测试:
	test_static.exe

.PHONY: all clean test
