#include <stdio.h>
#include <windows.h>
#include <stdint.h>

// 定义函数指针类型
typedef void (*AES128_CMAC_Calculate_Func)(const uint8_t *key, const uint8_t *message, size_t message_len, uint8_t *mac);
typedef int (*AES128_CMAC_Test_Func)(void);

// 辅助函数：打印十六进制数据
void print_hex(const char* label, const uint8_t* data, size_t len) {
    printf("%s: ", label);
    for(size_t i = 0; i < len; i++) {
        printf("%02X ", data[i]);
        if((i + 1) % 16 == 0) printf("\n");
    }
    if(len % 16 != 0) printf("\n");
}

int main(void)
{
    // 加载DLL
    HMODULE hDll = LoadLibrary("aes128_cmac.dll");
    if (hDll == NULL) {
        printf("无法加载DLL文件 aes128_cmac.dll\n");
        printf("错误代码: %lu\n", GetLastError());
        return 1;
    }

    // 获取函数地址
    AES128_CMAC_Calculate_Func AES128_CMAC_Calculate = 
        (AES128_CMAC_Calculate_Func)GetProcAddress(hDll, "AES128_CMAC_Calculate");
    
    AES128_CMAC_Test_Func AES128_CMAC_Test = 
        (AES128_CMAC_Test_Func)GetProcAddress(hDll, "AES128_CMAC_Test");

    if (AES128_CMAC_Calculate == NULL) {
        printf("无法找到函数 'AES128_CMAC_Calculate'\n");
        printf("错误代码: %lu\n", GetLastError());
        FreeLibrary(hDll);
        return 1;
    }

    if (AES128_CMAC_Test == NULL) {
        printf("无法找到函数 'AES128_CMAC_Test'\n");
        printf("错误代码: %lu\n", GetLastError());
        FreeLibrary(hDll);
        return 1;
    }

    printf("=== AES128-CMAC DLL 测试程序 ===\n\n");

    // 使用你提供的密钥和种子
    uint8_t our_key[16] = {
        0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
        0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
    };

    uint8_t test_message[16] = {
        0x1B, 0x36, 0xB4, 0xD3, 0x72, 0xB0, 0x33, 0xEA,
        0x7E, 0x2F, 0xD7, 0x96, 0x25, 0xCC, 0xD0, 0x0C
    };

    uint8_t mac_result[16];

    // 打印输入数据
    print_hex(" (Key)", our_key, 16);
    print_hex(" (Message)", test_message, 16);

    // 计算CMAC
    printf("\nAES128-CMAC...\n");
    AES128_CMAC_Calculate(our_key, test_message, 16, mac_result);

    // 打印结果
    print_hex("CMAC", mac_result, 16);

    // 运行内置测试
    printf("\n运行内置测试函数...\n");
    int test_result = AES128_CMAC_Test();
    printf("内置测试结果: %s\n", (test_result == 0) ? "通过" : "失败");

    // 测试不同长度的消息
    printf("\n=== 测试不同长度的消息 ===\n");
    
    // 测试空消息
    uint8_t empty_mac[16];
    AES128_CMAC_Calculate(our_key, NULL, 0, empty_mac);
    print_hex("空消息CMAC", empty_mac, 16);

    // 测试8字节消息
    uint8_t short_message[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
    uint8_t short_mac[16];
    AES128_CMAC_Calculate(our_key, short_message, 8, short_mac);
    print_hex("8字节消息", short_message, 8);
    print_hex("8字节消息CMAC", short_mac, 16);

    // 测试32字节消息
    uint8_t long_message[32];
    for(int i = 0; i < 32; i++) {
        long_message[i] = i + 1;
    }
    uint8_t long_mac[16];
    AES128_CMAC_Calculate(our_key, long_message, 32, long_mac);
    print_hex("32字节消息", long_message, 32);
    print_hex("32字节消息CMAC", long_mac, 16);

    // 释放DLL
    FreeLibrary(hDll);
    printf("\nAES128-CMAC DLL测试完成!\n");
    
    return 0;
}
