#include <stdio.h>
#include <windows.h>

// 定义函数指针类型
typedef int (*AddFunction)(int, int);

int main(void)
{
    // 加载DLL
    HMODULE hDll = LoadLibrary("add.dll");
    if (hDll == NULL) {
        printf("无法加载DLL文件 add.dll\n");
        printf("错误代码: %lu\n", GetLastError());
        return 1;
    }

    // 获取函数地址
    AddFunction add = (AddFunction)GetProcAddress(hDll, "add");
    if (add == NULL) {
        printf("无法找到函数 'add'\n");
        printf("错误代码: %lu\n", GetLastError());
        FreeLibrary(hDll);
        return 1;
    }

    // 测试函数
    printf("测试DLL中的add函数:\n");
    printf("add(3, 2) = %d\n", add(3, 2));
    printf("add(10, 20) = %d\n", add(10, 20));
    printf("add(-5, 8) = %d\n", add(-5, 8));
    printf("add(0, 0) = %d\n", add(0, 0));

    // 释放DLL
    FreeLibrary(hDll);
    printf("DLL测试完成!\n");
    
    return 0;
}
