/**************************************************************************************************/
/**
 * @file      : aes128_cmac.c
 * @brief     : Standalone AES128-CMAC implementation
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR>
 *
 * @note      : This implementation follows RFC 4493 - The AES-CMAC Algorithm
 *              Contains integrated AES128 ECB implementation
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#define BUILDING_AES_DLL
#include "aes128_cmac.h"
#include <string.h>

/* CMAC constants */
#define CMAC_RB_CONSTANT 0x87  /* Rb constant for GF(2^128) */

/* AES128 constants and implementation */
#define AES_KEYLEN 16
#define AES_keyExpSize 176

/* AES context structure */
typedef struct {
    uint8_t RoundKey[AES_keyExpSize];
} AES_ctx;

/* AES S-box */
static const uint8_t sbox[256] = {
    0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5, 0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
    0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0, 0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
    0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc, 0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
    0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a, 0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
    0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0, 0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
    0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b, 0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
    0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85, 0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
    0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5, 0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
    0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17, 0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
    0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88, 0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
    0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c, 0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
    0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9, 0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
    0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6, 0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
    0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e, 0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
    0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94, 0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
    0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68, 0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16
};

/* Round constants */
static const uint8_t Rcon[11] = {
    0x8d, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36
};

/* AES helper functions */
static uint8_t xtime(uint8_t x)
{
    return ((x<<1) ^ (((x>>7) & 1) * 0x1b));
}

static void SubBytes(uint8_t* state)
{
    for(int i = 0; i < 16; i++) {
        state[i] = sbox[state[i]];
    }
}

static void ShiftRows(uint8_t* state)
{
    uint8_t temp;

    /* Row 1 */
    temp = state[1];
    state[1] = state[5];
    state[5] = state[9];
    state[9] = state[13];
    state[13] = temp;

    /* Row 2 */
    temp = state[2];
    state[2] = state[10];
    state[10] = temp;
    temp = state[6];
    state[6] = state[14];
    state[14] = temp;

    /* Row 3 */
    temp = state[3];
    state[3] = state[15];
    state[15] = state[11];
    state[11] = state[7];
    state[7] = temp;
}

static void MixColumns(uint8_t* state)
{
    uint8_t i;
    uint8_t Tmp, Tm, t;
    for (i = 0; i < 4; ++i) {
        t   = state[i*4+0];
        Tmp = state[i*4+0] ^ state[i*4+1] ^ state[i*4+2] ^ state[i*4+3] ;
        Tm  = state[i*4+0] ^ state[i*4+1] ; Tm = xtime(Tm);  state[i*4+0] ^= Tm ^ Tmp ;
        Tm  = state[i*4+1] ^ state[i*4+2] ; Tm = xtime(Tm);  state[i*4+1] ^= Tm ^ Tmp ;
        Tm  = state[i*4+2] ^ state[i*4+3] ; Tm = xtime(Tm);  state[i*4+2] ^= Tm ^ Tmp ;
        Tm  = state[i*4+3] ^ t ;            Tm = xtime(Tm);  state[i*4+3] ^= Tm ^ Tmp ;
    }
}

static void AddRoundKey(uint8_t round, uint8_t* state, const uint8_t* RoundKey)
{
    for(int i = 0; i < 16; ++i) {
        state[i] ^= RoundKey[(round * 16) + i];
    }
}

static void KeyExpansion(uint8_t* RoundKey, const uint8_t* Key)
{
    unsigned i, j, k;
    uint8_t tempa[4];

    /* First round key is the key itself */
    for (i = 0; i < 16; ++i) {
        RoundKey[i] = Key[i];
    }

    /* All other round keys are found from the previous round keys */
    for (i = 16; i < AES_keyExpSize; i += 4) {
        k = (i / 4) - 1;
        for (j = 0; j < 4; ++j) {
            tempa[j] = RoundKey[(k * 4) + j];
        }

        if (i % 16 == 0) {
            /* RotWord */
            const uint8_t u8tmp = tempa[0];
            tempa[0] = tempa[1];
            tempa[1] = tempa[2];
            tempa[2] = tempa[3];
            tempa[3] = u8tmp;

            /* SubWord */
            tempa[0] = sbox[tempa[0]];
            tempa[1] = sbox[tempa[1]];
            tempa[2] = sbox[tempa[2]];
            tempa[3] = sbox[tempa[3]];

            tempa[0] = tempa[0] ^ Rcon[i/16];
        }

        j = i - 16;
        RoundKey[i + 0] = RoundKey[j + 0] ^ tempa[0];
        RoundKey[i + 1] = RoundKey[j + 1] ^ tempa[1];
        RoundKey[i + 2] = RoundKey[j + 2] ^ tempa[2];
        RoundKey[i + 3] = RoundKey[j + 3] ^ tempa[3];
    }
}

static void AES_init_ctx(AES_ctx* ctx, const uint8_t* key)
{
    KeyExpansion(ctx->RoundKey, key);
}

static void AES_ECB_encrypt(const AES_ctx* ctx, uint8_t* buf)
{
    uint8_t round = 0;

    /* Add the First round key to the state before starting the rounds */
    AddRoundKey(0, buf, ctx->RoundKey);

    /* There will be 9 rounds */
    for (round = 1; ; ++round) {
        SubBytes(buf);
        ShiftRows(buf);
        if (round == 10) {
            break;
        }
        MixColumns(buf);
        AddRoundKey(round, buf, ctx->RoundKey);
    }
    /* Add round key to last round */
    AddRoundKey(10, buf, ctx->RoundKey);
}

/**
 * @brief XOR two 16-byte blocks
 * @param a: First block
 * @param b: Second block  
 * @param result: Output block (a XOR b)
 */
void AES128_CMAC_XOR_Block(const uint8_t *a, const uint8_t *b, uint8_t *result)
{
    for(int i = 0; i < AES128_BLOCK_SIZE; i++) {
        result[i] = a[i] ^ b[i];
    }
}

/**
 * @brief Left shift a 16-byte block by 1 bit
 * @param input: Input block
 * @param output: Output block (input << 1)
 */
void AES128_CMAC_Left_Shift(const uint8_t *input, uint8_t *output)
{
    uint8_t carry = 0;
    
    for(int i = AES128_BLOCK_SIZE - 1; i >= 0; i--) {
        uint8_t new_carry = (input[i] & 0x80) ? 1 : 0;
        output[i] = (input[i] << 1) | carry;
        carry = new_carry;
    }
}

/**
 * @brief Generate CMAC subkeys K1 and K2 from AES key
 * @param key: 16-byte AES128 key
 * @param k1: Output buffer for subkey K1 (16 bytes)
 * @param k2: Output buffer for subkey K2 (16 bytes)
 */
void AES128_CMAC_Generate_Subkeys(const uint8_t *key, uint8_t *k1, uint8_t *k2)
{
    AES_ctx ctx;
    uint8_t zero_block[AES128_BLOCK_SIZE] = {0};
    uint8_t l[AES128_BLOCK_SIZE];
    
    /* Initialize AES context with key */
    AES_init_ctx(&ctx, key);
    
    /* L = AES_K(0^128) */
    memcpy(l, zero_block, AES128_BLOCK_SIZE);
    AES_ECB_encrypt(&ctx, l);
    
    /* Generate K1 */
    AES128_CMAC_Left_Shift(l, k1);
    if(l[0] & 0x80) {
        k1[AES128_BLOCK_SIZE - 1] ^= CMAC_RB_CONSTANT;
    }
    
    /* Generate K2 */
    AES128_CMAC_Left_Shift(k1, k2);
    if(k1[0] & 0x80) {
        k2[AES128_BLOCK_SIZE - 1] ^= CMAC_RB_CONSTANT;
    }
}

/**
 * @brief Calculate AES128-CMAC for a message
 * @param key: 16-byte AES128 key
 * @param message: Input message data
 * @param message_len: Length of message in bytes
 * @param mac: Output buffer for 16-byte MAC
 */
void AES128_CMAC_Calculate(const uint8_t *key, const uint8_t *message, size_t message_len, uint8_t *mac)
{
    AES_ctx ctx;
    uint8_t k1[AES128_BLOCK_SIZE], k2[AES128_BLOCK_SIZE];
    uint8_t x[AES128_BLOCK_SIZE] = {0};
    uint8_t y[AES128_BLOCK_SIZE];
    uint8_t last_block[AES128_BLOCK_SIZE];
    size_t complete_blocks;
    size_t remaining_bytes;
    
    /* Initialize AES context */
    AES_init_ctx(&ctx, key);
    
    /* Generate subkeys */
    AES128_CMAC_Generate_Subkeys(key, k1, k2);
    
    /* Calculate number of complete blocks */
    complete_blocks = message_len / AES128_BLOCK_SIZE;
    remaining_bytes = message_len % AES128_BLOCK_SIZE;
    
    /* Process complete blocks except the last one */
    for(size_t i = 0; i < complete_blocks; i++) {
        /* Check if this is the last block */
        if(i == complete_blocks - 1 && remaining_bytes == 0) {
            /* This is the last complete block, handle it separately */
            break;
        }
        
        /* X_i = X_{i-1} XOR M_i */
        AES128_CMAC_XOR_Block(x, &message[i * AES128_BLOCK_SIZE], y);
        
        /* X_i = AES_K(Y_i) */
        memcpy(x, y, AES128_BLOCK_SIZE);
        AES_ECB_encrypt(&ctx, x);
    }
    
    /* Prepare last block */
    memset(last_block, 0, AES128_BLOCK_SIZE);
    
    if(remaining_bytes == 0 && message_len > 0) {
        /* Last block is complete */
        memcpy(last_block, &message[(complete_blocks - 1) * AES128_BLOCK_SIZE], AES128_BLOCK_SIZE);
        AES128_CMAC_XOR_Block(last_block, k1, last_block);
    } else {
        /* Last block is incomplete or message is empty */
        if(message_len > 0) {
            memcpy(last_block, &message[complete_blocks * AES128_BLOCK_SIZE], remaining_bytes);
        }
        
        /* Apply padding: append '1' bit followed by '0' bits */
        if(remaining_bytes < AES128_BLOCK_SIZE) {
            last_block[remaining_bytes] = 0x80;
        }
        
        AES128_CMAC_XOR_Block(last_block, k2, last_block);
    }
    
    /* Final step */
    AES128_CMAC_XOR_Block(x, last_block, y);
    memcpy(mac, y, AES128_BLOCK_SIZE);
    AES_ECB_encrypt(&ctx, mac);
}

/**
 * @brief Test function for AES128-CMAC implementation
 * @return 0 if test passes, non-zero if test fails
 */
int AES128_CMAC_Test(void)
{


    uint8_t result[16];


    /* Test with our specific key and a sample message */
    uint8_t our_key[16] = {
        0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
        0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
    };

    uint8_t test_message[16] = {
        0x1B, 0x36, 0xB4, 0xD3, 0x72, 0xB0, 0x33, 0xEA,
        0x7E, 0x2F, 0xD7, 0x96, 0x25, 0xCC, 0xD0, 0x0C
    };

    AES128_CMAC_Calculate(our_key, test_message, 16, result);

}
