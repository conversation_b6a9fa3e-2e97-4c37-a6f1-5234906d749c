@echo off
echo Building AES128-CMAC DLL and test programs...

REM Compile AES128-CMAC DLL
echo 1. Compiling aes128_cmac.dll...
gcc -shared -o aes128_cmac.dll aes128_cmac.c "-Wl,--out-implib,aes128_cmac.lib"

if %errorlevel% neq 0 (
    echo AES DLL compilation failed!
    pause
    exit /b 1
)

echo AES DLL compilation successful!

REM Compile dynamic linking test program
echo 2. Compiling dynamic linking test program...
gcc -o test_aes_dll.exe test_aes_dll.c

if %errorlevel% neq 0 (
    echo Dynamic linking test program compilation failed!
    pause
    exit /b 1
)

echo Dynamic linking test program compilation successful!

REM Compile static linking test program
echo 3. Compiling static linking test program...
gcc -o test_aes_static.exe test_aes_static.c aes128_cmac.lib

if %errorlevel% neq 0 (
    echo Static linking test program compilation failed!
    pause
    exit /b 1
)

echo Static linking test program compilation successful!

echo.
echo Compilation complete! Generated files:
echo - aes128_cmac.dll (AES128-CMAC Dynamic Link Library)
echo - aes128_cmac.lib (Import Library)
echo - test_aes_dll.exe (Dynamic linking test program)
echo - test_aes_static.exe (Static linking test program)
echo.
echo Run tests:
echo 1. test_aes_dll.exe (requires aes128_cmac.dll in same directory)
echo 2. test_aes_static.exe (standalone)

pause
