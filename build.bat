@echo off
echo Building DLL and test programs...

REM Compile DLL
echo 1. Compiling add.dll...
gcc -shared -o add.dll add.c "-Wl,--out-implib,add.lib"

if %errorlevel% neq 0 (
    echo DLL compilation failed!
    pause
    exit /b 1
)

echo DLL compilation successful!

REM Compile dynamic linking test program
echo 2. Compiling dynamic linking test program...
gcc -o test_dll.exe test_dll.c

if %errorlevel% neq 0 (
    echo Dynamic linking test program compilation failed!
    pause
    exit /b 1
)

echo Dynamic linking test program compilation successful!

REM Compile static linking test program
echo 3. Compiling static linking test program...
gcc -o test_static.exe test_static.c add.lib

if %errorlevel% neq 0 (
    echo Static linking test program compilation failed!
    pause
    exit /b 1
)

echo Static linking test program compilation successful!

echo.
echo Compilation complete! Generated files:
echo - add.dll (Dynamic Link Library)
echo - add.lib (Import Library)
echo - test_dll.exe (Dynamic linking test program)
echo - test_static.exe (Static linking test program)
echo.
echo Run tests:
echo 1. test_dll.exe (requires add.dll in same directory)
echo 2. test_static.exe (standalone)

pause
