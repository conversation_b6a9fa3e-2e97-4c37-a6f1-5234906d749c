#include <stdio.h>
#include <stdint.h>
#include "aes128_cmac.h"

// 辅助函数：打印十六进制数据
void print_hex(const char* label, const uint8_t* data, size_t len) {
    printf("%s: ", label);
    for(size_t i = 0; i < len; i++) {
        printf("%02X ", data[i]);
        if((i + 1) % 16 == 0) printf("\n");
    }
    if(len % 16 != 0) printf("\n");
}

int main(void)
{
    printf("=== AES128-CMAC 静态链接测试程序 ===\n\n");

    // 使用你提供的密钥和种子
    uint8_t our_key[16] = {
        0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
        0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
    };

    uint8_t test_message[16] = {
        0x1B, 0x36, 0xB4, 0xD3, 0x72, 0xB0, 0x33, 0xEA,
        0x7E, 0x2F, 0xD7, 0x96, 0x25, 0xCC, 0xD0, 0x0C
    };

    uint8_t mac_result[16];

    // 打印输入数据
    print_hex("密钥 (Key)", our_key, 16);
    print_hex("输入种子 (Message)", test_message, 16);

    // 计算CMAC
    printf("\n正在计算AES128-CMAC...\n");
    AES128_CMAC_Calculate(our_key, test_message, 16, mac_result);

    // 打印结果
    print_hex("CMAC结果", mac_result, 16);

    // 运行内置测试
    printf("\n运行内置测试函数...\n");
    int test_result = AES128_CMAC_Test();
    printf("内置测试结果: %s\n", (test_result == 0) ? "通过" : "失败");

    // 测试不同长度的消息
    printf("\n=== 测试不同长度的消息 ===\n");
    
    // 测试空消息
    uint8_t empty_mac[16];
    AES128_CMAC_Calculate(our_key, NULL, 0, empty_mac);
    print_hex("空消息CMAC", empty_mac, 16);

    // 测试8字节消息
    uint8_t short_message[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
    uint8_t short_mac[16];
    AES128_CMAC_Calculate(our_key, short_message, 8, short_mac);
    print_hex("8字节消息", short_message, 8);
    print_hex("8字节消息CMAC", short_mac, 16);

    // 测试32字节消息
    uint8_t long_message[32];
    for(int i = 0; i < 32; i++) {
        long_message[i] = i + 1;
    }
    uint8_t long_mac[16];
    AES128_CMAC_Calculate(our_key, long_message, 32, long_mac);
    print_hex("32字节消息", long_message, 32);
    print_hex("32字节消息CMAC", long_mac, 16);

    printf("\nAES128-CMAC 静态链接测试完成!\n");
    
    return 0;
}
