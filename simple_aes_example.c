#include <stdio.h>
#include <windows.h>
#include <stdint.h>

// 定义函数指针类型
typedef void (*AES128_CMAC_Calculate_Func)(const uint8_t *key, const uint8_t *message, size_t message_len, uint8_t *mac);

int main(void)
{
    // 加载DLL
    HMODULE hDll = LoadLibrary("aes128_cmac.dll");
    if (hDll == NULL) {
        printf("Error: Cannot load aes128_cmac.dll\n");
        return 1;
    }

    // 获取函数地址
    AES128_CMAC_Calculate_Func AES128_CMAC_Calculate = 
        (AES128_CMAC_Calculate_Func)GetProcAddress(hDll, "AES128_CMAC_Calculate");

    if (AES128_CMAC_Calculate == NULL) {
        printf("Error: Cannot find function 'AES128_CMAC_Calculate'\n");
        FreeLibrary(hDll);
        return 1;
    }

    // 你的密钥和种子
    uint8_t our_key[16] = {
        0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
        0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
    };

    uint8_t test_message[16] = {
        0x1B, 0x36, 0xB4, 0xD3, 0x72, 0xB0, 0x33, 0xEA,
        0x7E, 0x2F, 0xD7, 0x96, 0x25, 0xCC, 0xD0, 0x0C
    };

    uint8_t mac_result[16];

    // 计算CMAC
    AES128_CMAC_Calculate(our_key, test_message, 16, mac_result);

    // 打印结果
    printf("AES128-CMAC Result: ");
    for(int i = 0; i < 16; i++) {
        printf("%02X ", mac_result[i]);
    }
    printf("\n");

    // 释放DLL
    FreeLibrary(hDll);
    
    return 0;
}
